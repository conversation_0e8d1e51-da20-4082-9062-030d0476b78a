"use client"

import { getModel, getSiteConfig, getThemeIndex } from "@/src/lib/content"
import { MarkdownRenderer } from "@/src/components/MarkdownRenderer"
import { MaturityTable } from "@/src/components/MaturityTable"
import { Breadcrumbs } from "@/src/components/Breadcrumbs"
import { Button } from "@/components/ui/button"
import { Download, FileText, Printer } from "lucide-react"
import { notFound } from "next/navigation"
import { modelToCsv, modelToMarkdown } from "@/src/lib/export"

interface ModelPageProps {
  params: { theme: string; model: string }
}

export default function ModelPage({ params }: ModelPageProps) {
  try {
    const site = getSiteConfig()
    const theme = getThemeIndex(params.theme)
    const model = getModel(params.theme, params.model)
    const levels = model.levels?.length ? model.levels : site.defaultLevels

    const csvData = modelToCsv(model, levels)
    const markdownData = modelToMarkdown(model, levels)

    return (
      <div className="space-y-8 pl-6">
        <Breadcrumbs
          items={[
            { label: "Home", href: "/" },
            { label: theme.title, href: `/t/${params.theme}` },
            { label: model.title },
          ]}
        />

        {/* Header */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-balance">{model.title}</h1>
              {model.shortDescription && (
                <p className="text-lg text-muted-foreground text-pretty mt-2">{model.shortDescription}</p>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => window.print()}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const blob = new Blob([csvData], { type: "text/csv" })
                  const url = URL.createObjectURL(blob)
                  const a = document.createElement("a")
                  a.href = url
                  a.download = `${model.modelId}-maturity-model.csv`
                  a.click()
                  URL.revokeObjectURL(url)
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(markdownData)
                }}
              >
                <FileText className="h-4 w-4 mr-2" />
                Copy Markdown
              </Button>
            </div>
          </div>
        </div>

        {/* Overview */}
        {model.overviewMd && (
          <section>
            <MarkdownRenderer content={model.overviewMd} />
          </section>
        )}

        {/* Maturity Table */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-balance">Maturity Assessment</h2>
          <MaturityTable levels={levels} dimensions={model.dimensions} theme={params.theme} modelId={params.model} />
        </section>

        {/* How to Assess */}
        {model.howToAssessMd && (
          <section>
            <h2 className="text-2xl font-semibold mb-4 text-balance">How to Assess</h2>
            <MarkdownRenderer content={model.howToAssessMd} />
          </section>
        )}

        {/* Improvement Paths */}
        {model.improvementPathsMd && (
          <section>
            <h2 className="text-2xl font-semibold mb-4 text-balance">Improvement Paths</h2>
            <MarkdownRenderer content={model.improvementPathsMd} />
          </section>
        )}

        {/* References */}
        {model.references?.length && (
          <section>
            <h2 className="text-2xl font-semibold mb-4 text-balance">References</h2>
            <ul className="space-y-2">
              {model.references.map((ref, index) => (
                <li key={index}>
                  <a href={ref.url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                    {ref.label}
                  </a>
                </li>
              ))}
            </ul>
          </section>
        )}

        {/* Metadata */}
        {(model.version || model.lastUpdated || model.tags) && (
          <section className="pt-8 border-t border-border">
            <div className="text-sm text-muted-foreground space-y-2">
              {model.version && <div>Version: {model.version}</div>}
              {model.lastUpdated && <div>Last Updated: {model.lastUpdated}</div>}
              {model.tags && <div>Tags: {model.tags.map((tag) => `#${tag}`).join(", ")}</div>}
            </div>
          </section>
        )}
      </div>
    )
  } catch (error) {
    notFound()
  }
}
