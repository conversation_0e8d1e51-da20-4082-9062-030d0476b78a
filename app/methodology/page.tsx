import { Breadcrumbs } from "@/src/components/Breadcrumbs"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/src/components/MarkdownRenderer"

const methodologyContent = `
# Methodology

## Our Approach to Maturity Models

Software maturity models provide a structured framework for assessing and improving organizational capabilities. Our models are designed to be practical, actionable, and based on industry best practices.

### Design Principles

**1. Evidence-Based Levels**
Each maturity level is defined by observable, measurable characteristics rather than aspirational goals.

**2. Incremental Progression**
Organizations can advance one level at a time, with clear guidance on what needs to change.

**3. Context-Aware Assessment**
Models account for different organizational contexts, sizes, and industry requirements.

**4. Actionable Guidance**
Every assessment includes specific next steps and improvement recommendations.

### Maturity Level Framework

Our 6-level framework (0-5) is based on the Capability Maturity Model (CMM) but adapted for modern software development:

- **Level 0 (Absent)**: No systematic approach exists
- **Level 1 (Initial)**: Ad-hoc processes with inconsistent results
- **Level 2 (Managed)**: Basic processes are established and followed
- **Level 3 (Defined)**: Standardized processes with consistent application
- **Level 4 (Quantitatively Managed)**: Processes are measured and controlled
- **Level 5 (Optimizing)**: Continuous improvement with data-driven optimization

### Assessment Guidelines

**Preparation**
1. Gather relevant documentation and artifacts
2. Identify key stakeholders for interviews
3. Collect quantitative metrics where available

**Assessment Process**
1. **Document Review** - Examine existing processes and guidelines
2. **Stakeholder Interviews** - Understand current practices and pain points
3. **Observation** - Review actual practices vs. documented processes
4. **Metric Analysis** - Validate qualitative findings with data

**Scoring Guidelines**
- Assess each dimension independently
- Use the lowest common denominator (if practices vary across teams)
- Focus on consistent, sustainable practices rather than one-off initiatives
- Consider both formal processes and actual implementation

### Using the Results

**Gap Analysis**
Identify the difference between current state and desired target levels.

**Prioritization**
Focus on foundational capabilities before advancing to higher levels.

**Roadmap Planning**
Create a realistic timeline for improvements based on organizational capacity.

**Progress Tracking**
Regular re-assessment to measure improvement and adjust plans.

## Model Development Process

Our models are developed through:

1. **Literature Review** - Analysis of existing frameworks and research
2. **Industry Consultation** - Input from practitioners and experts
3. **Pilot Testing** - Validation with real organizations
4. **Iterative Refinement** - Continuous improvement based on feedback

## Contributing

We welcome contributions to improve these models. Please see our GitHub repository for guidelines on suggesting improvements or adding new models.
`

export default function MethodologyPage() {
  return (
    <div className="space-y-6">
      <Breadcrumbs items={[{ label: "Home", href: "/" }, { label: "Methodology" }]} />

      <div className="max-w-4xl">
        <MarkdownRenderer content={methodologyContent} />
      </div>
    </div>
  )
}
