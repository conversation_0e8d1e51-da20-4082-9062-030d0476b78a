export function modelToCsv(model: any, levels: any[]) {
  const headers = ["Dimension", ...levels.map((l) => l.label)]
  const rows = model.dimensions.map((d: any) => [
    csvEscape(d.label),
    ...levels.map((l) => csvEscape(d.cells[l.id] || "")),
  ])
  return [headers, ...rows].map((r) => r.join(",")).join("\n")
}

export function modelToMarkdown(model: any, levels: any[]) {
  let md = `# ${model.title}\n\n`

  if (model.shortDescription) {
    md += `${model.shortDescription}\n\n`
  }

  md += "## Maturity Levels\n\n"
  md += "| Dimension | " + levels.map((l) => l.label).join(" | ") + " |\n"
  md += "|" + "---|".repeat(levels.length + 1) + "\n"

  model.dimensions.forEach((d: any) => {
    md += `| **${d.label}** | `
    md += levels.map((l) => (d.cells[l.id] || "").replace(/\|/g, "\\|")).join(" | ")
    md += " |\n"
  })

  return md
}

function csvEscape(str: string): string {
  const needsEscape = /[",\n\r]/.test(str)
  const escaped = str.replace(/"/g, '""')
  return needsEscape ? `"${escaped}"` : escaped
}
