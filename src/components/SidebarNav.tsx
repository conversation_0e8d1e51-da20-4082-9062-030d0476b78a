"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronDown, ChevronRight, Menu, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import Image from "next/image"

interface ThemeIndex {
  theme: string
  title: string
  models: { modelId: string; title: string }[]
}

interface SidebarNavProps {
  themes: ThemeIndex[]
}

export function SidebarNav({ themes }: SidebarNavProps) {
  const pathname = usePathname()
  const [expandedThemes, setExpandedThemes] = useState<Set<string>>(new Set())
  const [isMobileOpen, setIsMobileOpen] = useState(false)

  const toggleTheme = (theme: string) => {
    const newExpanded = new Set(expandedThemes)
    if (newExpanded.has(theme)) {
      newExpanded.delete(theme)
    } else {
      newExpanded.add(theme)
    }
    setExpandedThemes(newExpanded)
  }

  const isActiveModel = (theme: string, modelId: string) => {
    return pathname.startsWith(`/t/${theme}/${modelId}`)
  }

  const isActiveTheme = (theme: string) => {
    return pathname.startsWith(`/t/${theme}`)
  }

  const NavContent = () => (
    <nav className="p-4 space-y-6 h-full overflow-y-auto">
      <div className="pb-4 border-b border-border">
        <Link
          href="/"
          className="flex items-center gap-3 hover:opacity-80 transition-opacity"
          onClick={() => setIsMobileOpen(false)}
        >
          <Image src="/logo.png" alt="Software Maturity Models" width={40} height={40} className="flex-shrink-0" />
          <div className="flex flex-col">
            <span className="font-semibold text-sm leading-tight">Software Maturity</span>
            <span className="font-semibold text-sm leading-tight">Models</span>
          </div>
        </Link>
      </div>

      {/* Themes */}
      <div className="space-y-4">
        {themes.map((theme) => (
          <div key={theme.theme}>
            <button
              onClick={() => toggleTheme(theme.theme)}
              className={cn(
                "flex items-center justify-between w-full text-left p-2 rounded-md hover:bg-muted transition-colors",
                isActiveTheme(theme.theme) && "bg-muted",
              )}
            >
              <span className="font-medium text-sm">{theme.title}</span>
              {expandedThemes.has(theme.theme) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>

            {expandedThemes.has(theme.theme) && (
              <div className="mt-2 ml-4 space-y-1">
                {theme.models.map((model) => (
                  <Link
                    key={model.modelId}
                    href={`/t/${theme.theme}/${model.modelId}`}
                    className={cn(
                      "block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors",
                      isActiveModel(theme.theme, model.modelId) &&
                        "bg-primary text-primary-foreground hover:bg-primary/90",
                    )}
                    onClick={() => setIsMobileOpen(false)}
                  >
                    {model.title}
                  </Link>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Additional Pages */}
      <div className="pt-4 border-t border-border space-y-1">
        <Link
          href="/glossary"
          className={cn(
            "block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors",
            pathname === "/glossary" && "bg-primary text-primary-foreground",
          )}
          onClick={() => setIsMobileOpen(false)}
        >
          Glossary
        </Link>
        <Link
          href="/methodology"
          className={cn(
            "block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors",
            pathname === "/methodology" && "bg-primary text-primary-foreground",
          )}
          onClick={() => setIsMobileOpen(false)}
        >
          Methodology
        </Link>
        <Link
          href="/about"
          className={cn(
            "block px-3 py-2 text-sm rounded-md hover:bg-muted transition-colors",
            pathname === "/about" && "bg-primary text-primary-foreground",
          )}
          onClick={() => setIsMobileOpen(false)}
        >
          About
        </Link>
      </div>
    </nav>
  )

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="md:hidden fixed top-4 left-4 z-50"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        <span className="sr-only">Toggle navigation</span>
      </Button>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="md:hidden fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <aside
        className={cn(
          "md:hidden fixed left-0 top-0 h-full w-80 bg-background border-r border-border z-40 transform transition-transform duration-200",
          isMobileOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <NavContent />
      </aside>

      {/* Desktop sidebar */}
      <aside className="hidden md:block w-80 flex-shrink-0 border-r border-border bg-background">
        <NavContent />
      </aside>
    </>
  )
}
