"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, ExternalLink, Hash, Check } from "lucide-react"
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface Level {
  id: string
  label: string
  color?: string
}

interface Dimension {
  id: string
  label: string
  cells: Record<string, string>
  assessmentMd?: string
  nextStepsMd?: string
}

interface MaturityTableProps {
  levels: Level[]
  dimensions: Dimension[]
  theme: string
  modelId: string
}

function getLevelColor(levelId: string, level?: Level): string {
  if (level?.color) return level.color

  const colorMap: Record<string, string> = {
    "0": "#b91c1c", // red-700
    "1": "#dc2626", // red-600
    "2": "#d97706", // amber-600
    "3": "#16a34a", // green-600
    "4": "#0ea5e9", // sky-500
    "5": "#7c3aed", // violet-600
  }
  return colorMap[levelId] || "#64748b"
}

function CellContent({
  content,
  dimensionId,
  levelId,
}: {
  content?: string
  dimensionId: string
  levelId: string
}) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (!content) {
    return <div className="text-muted-foreground text-sm">—</div>
  }

  const shouldTruncate = content.length > 200
  const displayContent = shouldTruncate && !isExpanded ? content.slice(0, 200) + "…" : content

  return (
    <div className="space-y-2">
      <div className="text-sm leading-relaxed whitespace-pre-wrap">{displayContent}</div>
      {shouldTruncate && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="h-auto p-0 text-xs text-primary hover:underline"
        >
          {isExpanded ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              Show less
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              Show more
            </>
          )}
        </Button>
      )}
    </div>
  )
}

const levelDescriptions: Record<string, string> = {
  "0": "No systematic approach exists; practices are absent or ad-hoc",
  "1": "Basic practices exist but are inconsistent and informal",
  "2": "Practices are documented and followed with some consistency",
  "3": "Well-defined processes are consistently applied across teams",
  "4": "Practices are measured and managed with quantitative metrics",
  "5": "Continuous optimization based on feedback and innovation",
}

function getStorageKey(theme: string, modelId: string): string {
  return `maturity-levels-${theme}-${modelId}`
}

function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text)
}

function createAnchorId(dimensionId: string): string {
  return `dimension-${dimensionId.toLowerCase().replace(/[^a-z0-9]/g, "-")}`
}

export function MaturityTable({ levels, dimensions, theme, modelId }: MaturityTableProps) {
  const [selectedLevels, setSelectedLevels] = useState<Record<string, string>>({})
  const [hoveredColumn, setHoveredColumn] = useState<string | null>(null)
  const [copiedAnchor, setCopiedAnchor] = useState<string | null>(null)

  useEffect(() => {
    const storageKey = getStorageKey(theme, modelId)
    const saved = localStorage.getItem(storageKey)
    if (saved) {
      try {
        setSelectedLevels(JSON.parse(saved))
      } catch (e) {
        console.error("Failed to parse saved levels:", e)
      }
    }
  }, [theme, modelId])

  useEffect(() => {
    const storageKey = getStorageKey(theme, modelId)
    localStorage.setItem(storageKey, JSON.stringify(selectedLevels))
  }, [selectedLevels, theme, modelId])

  const handleLevelSelect = (dimensionId: string, levelId: string) => {
    setSelectedLevels((prev) => ({
      ...prev,
      [dimensionId]: prev[dimensionId] === levelId ? "" : levelId,
    }))
  }

  const handleCopyAnchor = (dimensionId: string) => {
    const anchorId = createAnchorId(dimensionId)
    const url = `${window.location.origin}${window.location.pathname}#${anchorId}`
    copyToClipboard(url)
    setCopiedAnchor(anchorId)
    setTimeout(() => setCopiedAnchor(null), 2000)
  }

  return (
    <TooltipProvider>
      <div className="w-full">
        {/* Mobile view - stacked cards */}
        <div className="block md:hidden space-y-6">
          {dimensions.map((dimension) => {
            const anchorId = createAnchorId(dimension.id)
            return (
              <div key={dimension.id} id={anchorId} className="border border-border rounded-lg overflow-hidden">
                <div className="bg-muted p-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-balance">{dimension.label}</h3>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyAnchor(dimension.id)}
                        className="h-auto p-1"
                      >
                        {copiedAnchor === anchorId ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Hash className="h-4 w-4" />
                        )}
                        <span className="sr-only">Copy link to dimension</span>
                      </Button>
                      <Button variant="ghost" size="sm" asChild className="h-auto p-1">
                        <a href={`/t/${theme}/${modelId}/d/${dimension.id}`}>
                          <ExternalLink className="h-4 w-4" />
                          <span className="sr-only">View dimension details</span>
                        </a>
                      </Button>
                    </div>
                  </div>
                </div>
                <div className="divide-y divide-border">
                  {levels.map((level) => {
                    const isSelected = selectedLevels[dimension.id] === level.id
                    return (
                      <div key={level.id} className="p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <button
                                onClick={() => handleLevelSelect(dimension.id, level.id)}
                                className={`w-6 h-6 rounded-full flex-shrink-0 cursor-pointer border-2 flex items-center justify-center transition-all ${
                                  isSelected
                                    ? "border-white shadow-lg scale-110"
                                    : "border-transparent hover:border-white/50"
                                }`}
                                style={{ backgroundColor: getLevelColor(level.id, level) }}
                              >
                                {isSelected && <Check className="h-3 w-3 text-white" />}
                              </button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="max-w-xs text-sm">
                                <strong>
                                  Level {level.id} - {level.label}:
                                </strong>
                                <br />
                                {levelDescriptions[level.id] || "Maturity level description"}
                                <br />
                                <em className="text-xs">Click to mark as current level</em>
                              </p>
                            </TooltipContent>
                          </Tooltip>
                          <span className="font-medium text-sm">{level.label}</span>
                        </div>
                        <CellContent
                          content={dimension.cells[level.id]}
                          dimensionId={dimension.id}
                          levelId={level.id}
                        />
                      </div>
                    )
                  })}
                </div>
              </div>
            )
          })}
        </div>

        {/* Desktop view - table */}
        <div className="hidden md:block overflow-auto rounded-lg border border-border">
          <style jsx>{`
            .maturity-table {
              position: relative;
            }
            .maturity-table th:first-child,
            .maturity-table td:first-child {
              position: sticky;
              left: 0;
              z-index: 10;
            }
            .maturity-table thead th:first-child {
              z-index: 30;
            }
            .maturity-table thead th {
              position: sticky;
              top: 0;
              z-index: 20;
            }
            .column-hover {
              background-color: hsl(var(--muted) / 0.3) !important;
            }
            .dark .column-hover {
              background-color: hsl(var(--muted) / 0.5) !important;
            }
          `}</style>
          <table className="maturity-table w-full border-collapse text-sm">
            <thead>
              <tr>
                <th className="bg-background text-left p-4 border-b border-border font-semibold min-w-[200px]">
                  Dimension
                </th>
                {levels.map((level) => (
                  <th
                    key={level.id}
                    className={`bg-background p-4 border-b border-border text-left font-semibold min-w-[200px] transition-colors ${
                      hoveredColumn === level.id ? "column-hover" : ""
                    }`}
                    style={{
                      borderTopColor: getLevelColor(level.id, level),
                      borderTopWidth: "3px",
                    }}
                    onMouseEnter={() => setHoveredColumn(level.id)}
                    onMouseLeave={() => setHoveredColumn(null)}
                  >
                    <div className="flex items-center gap-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className="w-3 h-3 rounded-full flex-shrink-0 cursor-help"
                            style={{ backgroundColor: getLevelColor(level.id, level) }}
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs text-sm">
                            <strong>
                              Level {level.id} - {level.label}:
                            </strong>
                            <br />
                            {levelDescriptions[level.id] || "Maturity level description"}
                          </p>
                        </TooltipContent>
                      </Tooltip>
                      <span className="text-balance">{level.label}</span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {dimensions.map((dimension, rowIndex) => {
                const anchorId = createAnchorId(dimension.id)
                return (
                  <tr
                    key={dimension.id}
                    id={anchorId}
                    className={`align-top hover:bg-muted/50 group ${rowIndex % 2 === 0 ? "bg-background" : "bg-muted/20"}`}
                  >
                    <th className="bg-inherit p-4 border-t border-border font-medium text-left">
                      <div className="flex items-center justify-between gap-2">
                        <span className="text-balance">{dimension.label}</span>
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyAnchor(dimension.id)}
                            className="h-auto p-1"
                          >
                            {copiedAnchor === anchorId ? (
                              <Check className="h-4 w-4 text-green-600" />
                            ) : (
                              <Hash className="h-4 w-4" />
                            )}
                            <span className="sr-only">Copy link to dimension</span>
                          </Button>
                          <Button variant="ghost" size="sm" asChild className="h-auto p-1">
                            <a href={`/t/${theme}/${modelId}/d/${dimension.id}`}>
                              <ExternalLink className="h-4 w-4" />
                              <span className="sr-only">View dimension details</span>
                            </a>
                          </Button>
                        </div>
                      </div>
                    </th>
                    {levels.map((level) => {
                      const isSelected = selectedLevels[dimension.id] === level.id
                      return (
                        <td
                          key={level.id}
                          className={`p-4 border-t border-border transition-colors ${
                            hoveredColumn === level.id ? "column-hover" : ""
                          }`}
                          onMouseEnter={() => setHoveredColumn(level.id)}
                          onMouseLeave={() => setHoveredColumn(null)}
                        >
                          <div className="relative">
                            {isSelected && (
                              <div className="absolute -top-2 -left-2 z-10">
                                <div
                                  className="w-4 h-4 rounded-full flex items-center justify-center border-2 border-white shadow-lg"
                                  style={{ backgroundColor: getLevelColor(level.id, level) }}
                                >
                                  <Check className="h-2 w-2 text-white" />
                                </div>
                              </div>
                            )}
                            <div
                              onClick={() => handleLevelSelect(dimension.id, level.id)}
                              className="cursor-pointer hover:bg-muted/30 rounded p-1 -m-1 transition-colors"
                              title="Click to mark as current level"
                            >
                              <CellContent
                                content={dimension.cells[level.id]}
                                dimensionId={dimension.id}
                                levelId={level.id}
                              />
                            </div>
                          </div>
                        </td>
                      )
                    })}
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </TooltipProvider>
  )
}
