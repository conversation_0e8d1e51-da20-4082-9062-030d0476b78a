"use client"

import { useState, useMemo, useRef, useEffect } from "react"
import { SearchIcon, X } from "lucide-react"
import { Button } from "@/components/ui/button"

interface SearchItem {
  theme: string
  modelId: string
  modelTitle: string
  dimensionId?: string
  dimensionLabel?: string
  text: string
  url: string
}

interface SearchProps {
  searchIndex: SearchItem[]
}

export function Search({ searchIndex }: SearchProps) {
  const [query, setQuery] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  const results = useMemo(() => {
    const trimmedQuery = query.trim().toLowerCase()
    if (!trimmedQuery || trimmedQuery.length < 2) return []

    return searchIndex
      .filter((item) => item.text.toLowerCase().includes(trimmedQuery))
      .slice(0, 10)
      .sort((a, b) => {
        // Prioritize exact matches in titles
        const aTitle = a.modelTitle.toLowerCase()
        const bTitle = b.modelTitle.toLowerCase()
        const aDimension = a.dimensionLabel?.toLowerCase() || ""
        const bDimension = b.dimensionLabel?.toLowerCase() || ""

        if (aTitle.includes(trimmedQuery) && !bTitle.includes(trimmedQuery)) return -1
        if (!aTitle.includes(trimmedQuery) && bTitle.includes(trimmedQuery)) return 1
        if (aDimension.includes(trimmedQuery) && !bDimension.includes(trimmedQuery)) return -1
        if (!aDimension.includes(trimmedQuery) && bDimension.includes(trimmedQuery)) return 1

        return 0
      })
  }, [query, searchIndex])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "/" && !isOpen) {
        e.preventDefault()
        setIsOpen(true)
        setTimeout(() => inputRef.current?.focus(), 100)
      }
      if (e.key === "Escape" && isOpen) {
        setIsOpen(false)
        setQuery("")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen])

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (resultsRef.current && !resultsRef.current.contains(e.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      return () => document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen])

  const handleResultClick = () => {
    setIsOpen(false)
    setQuery("")
  }

  return (
    <div className="relative">
      {/* Search trigger button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(true)}
        className="w-full md:w-64 justify-start text-muted-foreground"
      >
        <SearchIcon className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Search models...</span>
        <span className="sm:hidden">Search...</span>
        <kbd className="hidden md:inline-flex ml-auto pointer-events-none h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
          /
        </kbd>
      </Button>

      {/* Search modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl">
            <div ref={resultsRef} className="mx-4 bg-background border border-border rounded-lg shadow-lg">
              {/* Search input */}
              <div className="flex items-center border-b border-border px-4">
                <SearchIcon className="h-4 w-4 text-muted-foreground mr-3" />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Search models, dimensions, and content..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  className="flex-1 py-4 bg-transparent outline-none placeholder:text-muted-foreground"
                  autoFocus
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsOpen(false)
                    setQuery("")
                  }}
                  className="ml-2"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Search results */}
              <div className="max-h-96 overflow-y-auto">
                {query.trim().length < 2 ? (
                  <div className="p-4 text-center text-muted-foreground text-sm">
                    Type at least 2 characters to search
                  </div>
                ) : results.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground text-sm">No results found for "{query}"</div>
                ) : (
                  <div className="py-2">
                    {results.map((result, index) => (
                      <a
                        key={`${result.theme}-${result.modelId}-${result.dimensionId || "model"}-${index}`}
                        href={result.url}
                        onClick={handleResultClick}
                        className="block px-4 py-3 hover:bg-muted transition-colors border-b border-border last:border-b-0"
                      >
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm text-balance">{result.modelTitle}</div>
                            {result.dimensionLabel && (
                              <div className="text-xs text-muted-foreground mt-1">{result.dimensionLabel}</div>
                            )}
                            <div className="text-xs text-muted-foreground mt-1 capitalize">
                              {result.theme.replace("-", " ")}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground flex-shrink-0">
                            {result.dimensionId ? "Dimension" : "Model"}
                          </div>
                        </div>
                      </a>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="border-t border-border px-4 py-3 text-xs text-muted-foreground flex items-center justify-between">
                <div>
                  Press <kbd className="bg-muted px-1 rounded">ESC</kbd> to close
                </div>
                <div>
                  Press <kbd className="bg-muted px-1 rounded">/</kbd> to search
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
