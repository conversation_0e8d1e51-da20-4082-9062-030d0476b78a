{"@hookform/resolvers@3.10.0": {"licenses": "MIT", "repository": "https://github.com/react-hook-form/resolvers", "publisher": "bluebill1049", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@hookform/resolvers", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@hookform/resolvers/LICENSE"}, "@radix-ui/react-accordion@1.2.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-accordion", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-accordion/README.md"}, "@radix-ui/react-alert-dialog@1.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-alert-dialog", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-alert-dialog/README.md"}, "@radix-ui/react-aspect-ratio@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-aspect-ratio", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-aspect-ratio/README.md"}, "@radix-ui/react-avatar@1.1.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-avatar", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-avatar/README.md"}, "@radix-ui/react-checkbox@1.1.3": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-checkbox", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-checkbox/README.md"}, "@radix-ui/react-collapsible@1.1.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-collapsible", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-collapsible/README.md"}, "@radix-ui/react-context-menu@2.2.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-context-menu", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-context-menu/README.md"}, "@radix-ui/react-dialog@1.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-dialog", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-dialog/README.md"}, "@radix-ui/react-dropdown-menu@2.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-dropdown-menu", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-dropdown-menu/README.md"}, "@radix-ui/react-hover-card@1.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-hover-card", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-hover-card/README.md"}, "@radix-ui/react-label@2.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-label", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-label/README.md"}, "@radix-ui/react-menubar@1.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-menubar", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-menubar/README.md"}, "@radix-ui/react-navigation-menu@1.2.3": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-navigation-menu", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-navigation-menu/README.md"}, "@radix-ui/react-popover@1.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-popover", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-popover/README.md"}, "@radix-ui/react-progress@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-progress", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-progress/README.md"}, "@radix-ui/react-radio-group@1.2.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-radio-group", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-radio-group/README.md"}, "@radix-ui/react-scroll-area@1.2.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-scroll-area", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-scroll-area/README.md"}, "@radix-ui/react-select@2.1.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-select", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-select/README.md"}, "@radix-ui/react-separator@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-separator", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-separator/README.md"}, "@radix-ui/react-slider@1.2.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-slider", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-slider/README.md"}, "@radix-ui/react-slot@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-slot", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-slot/README.md"}, "@radix-ui/react-switch@1.1.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-switch", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-switch/README.md"}, "@radix-ui/react-tabs@1.1.2": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-tabs", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-tabs/README.md"}, "@radix-ui/react-toast@1.2.4": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toast", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toast/README.md"}, "@radix-ui/react-toggle-group@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toggle-group", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toggle-group/README.md"}, "@radix-ui/react-toggle@1.1.1": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toggle", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-toggle/README.md"}, "@radix-ui/react-tooltip@1.1.6": {"licenses": "MIT", "repository": "https://github.com/radix-ui/primitives", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-tooltip", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@radix-ui/react-tooltip/README.md"}, "@remix-run/react@2.17.0": {"licenses": "MIT", "repository": "https://github.com/remix-run/remix", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@remix-run/react", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@remix-run/react/LICENSE.md"}, "@sveltejs/kit@2.37.0": {"licenses": "MIT", "repository": "https://github.com/sveltejs/kit", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@sveltejs/kit", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@sveltejs/kit/LICENSE"}, "@tailwindcss/postcss@4.1.9": {"licenses": "MIT", "repository": "https://github.com/tailwindlabs/tailwindcss", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@tailwindcss/postcss", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@tailwindcss/postcss/LICENSE"}, "@types/node@22.0.0": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@types/node", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@types/node/LICENSE"}, "@types/react-dom@19.0.0": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@types/react-dom", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@types/react-dom/LICENSE"}, "@types/react@19.0.0": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@types/react", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@types/react/LICENSE"}, "@vercel/analytics@1.5.0": {"licenses": "MPL-2.0", "repository": "https://github.com/vercel/analytics", "path": "/Users/<USER>/git/software-maturity-models/node_modules/@vercel/analytics", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/@vercel/analytics/LICENSE"}, "autoprefixer@10.4.20": {"licenses": "MIT", "repository": "https://github.com/postcss/autoprefixer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/autoprefixer", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/autoprefixer/LICENSE"}, "class-variance-authority@0.7.1": {"licenses": "Apache-2.0", "repository": "https://github.com/joe-bell/cva", "publisher": "<PERSON>", "url": "https://joebell.co.uk", "path": "/Users/<USER>/git/software-maturity-models/node_modules/class-variance-authority", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/class-variance-authority/LICENSE"}, "clsx@2.1.1": {"licenses": "MIT", "repository": "https://github.com/lukeed/clsx", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/git/software-maturity-models/node_modules/clsx", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/clsx/license"}, "cmdk@1.0.4": {"licenses": "MIT", "repository": "https://github.com/pacocoursey/cmdk", "publisher": "<PERSON><PERSON>", "url": "https://github.com/pacocoursey", "path": "/Users/<USER>/git/software-maturity-models/node_modules/cmdk", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/cmdk/LICENSE.md"}, "date-fns@4.1.0": {"licenses": "MIT", "repository": "https://github.com/date-fns/date-fns", "path": "/Users/<USER>/git/software-maturity-models/node_modules/date-fns", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/date-fns/LICENSE.md"}, "embla-carousel-react@8.5.1": {"licenses": "MIT", "repository": "https://github.com/david<PERSON>le<PERSON>/embla-carousel", "publisher": "<PERSON>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/embla-carousel-react", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/embla-carousel-react/README.md"}, "geist@1.4.2": {"licenses": "MIT*", "repository": "https://github.com/vercel/geist-font", "path": "/Users/<USER>/git/software-maturity-models/node_modules/geist", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/geist/LICENSE.TXT"}, "input-otp@1.4.1": {"licenses": "MIT", "repository": "https://github.com/guilhermerodz/input-otp", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/input-otp", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/input-otp/README.md"}, "lucide-react@0.454.0": {"licenses": "ISC", "repository": "https://github.com/lucide-icons/lucide", "publisher": "<PERSON>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/lucide-react", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/lucide-react/LICENSE"}, "my-v0-project@0.1.0": {"licenses": "UNLICENSED", "private": true, "path": "/Users/<USER>/git/software-maturity-models", "licenseFile": "/Users/<USER>/git/software-maturity-models/LICENSE", "noticeFile": "/Users/<USER>/git/software-maturity-models/NOTICE"}, "next-themes@0.4.6": {"licenses": "MIT", "repository": "https://github.com/pacocoursey/next-themes", "path": "/Users/<USER>/git/software-maturity-models/node_modules/next-themes", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/next-themes/license.md"}, "next@15.2.4": {"licenses": "MIT", "repository": "https://github.com/vercel/next.js", "path": "/Users/<USER>/git/software-maturity-models/node_modules/next", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/next/license.md"}, "postcss@8.5.0": {"licenses": "MIT", "repository": "https://github.com/postcss/postcss", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/postcss", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/postcss/LICENSE"}, "react-day-picker@9.8.0": {"licenses": "MIT", "repository": "https://github.com/gpbl/react-day-picker", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react-day-picker", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react-day-picker/LICENSE"}, "react-dom@19.0.0": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react-dom", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react-dom/LICENSE"}, "react-hook-form@7.60.0": {"licenses": "MIT", "repository": "https://github.com/react-hook-form/react-hook-form", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "Bill", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react-hook-form", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react-hook-form/LICENSE"}, "react-markdown@10.1.0": {"licenses": "MIT", "repository": "https://github.com/remarkjs/react-markdown", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react-markdown", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react-markdown/license"}, "react-resizable-panels@2.1.7": {"licenses": "MIT", "repository": "https://github.com/bvaughn/react-resizable-panels", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react-resizable-panels", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react-resizable-panels/README.md"}, "react@19.0.0": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "path": "/Users/<USER>/git/software-maturity-models/node_modules/react", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/react/LICENSE"}, "recharts@2.15.4": {"licenses": "MIT", "repository": "https://github.com/recharts/recharts", "publisher": "recharts group", "path": "/Users/<USER>/git/software-maturity-models/node_modules/recharts", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/recharts/LICENSE"}, "rehype-sanitize@6.0.0": {"licenses": "MIT", "repository": "https://github.com/rehypejs/rehype-sanitize", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://wooorm.com", "path": "/Users/<USER>/git/software-maturity-models/node_modules/rehype-sanitize", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/rehype-sanitize/license"}, "remark-gfm@4.0.1": {"licenses": "MIT", "repository": "https://github.com/remarkjs/remark-gfm", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://wooorm.com", "path": "/Users/<USER>/git/software-maturity-models/node_modules/remark-gfm", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/remark-gfm/license"}, "sonner@1.7.4": {"licenses": "MIT", "repository": "https://github.com/emilkowalski/sonner", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/sonner", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/sonner/LICENSE.md"}, "svelte@5.38.6": {"licenses": "MIT", "repository": "https://github.com/sveltejs/svelte", "path": "/Users/<USER>/git/software-maturity-models/node_modules/svelte", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/svelte/LICENSE.md"}, "tailwind-merge@2.5.5": {"licenses": "MIT", "repository": "https://github.com/dcastil/tailwind-merge", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/tailwind-merge", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/tailwind-merge/LICENSE.md"}, "tailwindcss-animate@1.0.7": {"licenses": "MIT", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/tailwindcss-animate", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/tailwindcss-animate/LICENSE"}, "tailwindcss@4.1.9": {"licenses": "MIT", "repository": "https://github.com/tailwindlabs/tailwindcss", "path": "/Users/<USER>/git/software-maturity-models/node_modules/tailwindcss", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/tailwindcss/LICENSE"}, "tw-animate-css@1.3.3": {"licenses": "MIT", "repository": "https://github.com/Wombosvideo/tw-animate-css", "publisher": "<PERSON>", "url": "https://github.com/Wombosvideo", "path": "/Users/<USER>/git/software-maturity-models/node_modules/tw-animate-css", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/tw-animate-css/LICENSE"}, "typescript@5.0.2": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "publisher": "Microsoft Corp.", "path": "/Users/<USER>/git/software-maturity-models/node_modules/typescript", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/typescript/LICENSE.txt"}, "vaul@0.9.9": {"licenses": "MIT", "repository": "https://github.com/emilkowalski/vaul", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/vaul", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/vaul/LICENSE.md"}, "vue-router@4.5.1": {"licenses": "MIT", "repository": "https://github.com/vuejs/router", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/vue-router", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/vue-router/LICENSE"}, "vue@3.5.20": {"licenses": "MIT", "repository": "https://github.com/vuejs/core", "publisher": "<PERSON>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/vue", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/vue/LICENSE"}, "zod@3.25.67": {"licenses": "MIT", "repository": "https://github.com/colinhacks/zod", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/git/software-maturity-models/node_modules/zod", "licenseFile": "/Users/<USER>/git/software-maturity-models/node_modules/zod/LICENSE"}}